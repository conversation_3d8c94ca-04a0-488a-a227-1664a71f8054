# BLIP Specification (`blip-spec`)

**Welcome to the official specification for the BLIP animated image format!**

---

## What is BLIP?

**BLIP** is a modern, efficient, and unambiguous format for short, looping, muted animated images. Designed as a drop-in replacement for animated GIFs, `.blip` files offer dramatically better compression and quality by using next-generation video codecs, while retaining the intuitive, in-line behavior and cultural cachet of GIFs.

---

## Key Features

- **Infinitely Looping:** Every `.blip` file is a looping animation by design.
- **Autoplay & Muted:** Blips are always silent and start playing automatically—no audio, no controls.
- **Short & Sweet:** Ideal for memes, reactions, stickers, UI animations, and more.
- **Modern Compression:** Uses efficient video encoding (e.g., AV1), offering far smaller file sizes and better image quality than GIF.
- **Optional Transparency:** Full alpha channel support for crisp, modern visuals.
- **Clear Semantics:** The `.blip` extension and MIME type (`image/blip`) make usage and intent unambiguous.

---

## Quick Links

- [📄 The BLIP Format Specification](./spec.md) — The complete, up-to-date spec.
- [🛠️ Reference Implementation](https://github.com/blip-format/blip-reference) — Minimal encoder/decoder.
- [🔄 Converters](https://github.com/blip-format/blip-converter) — For migrating GIF/MP4 files.

---

## Why BLIP?

- **GIF is outdated.** BLIP is the 21st-century answer: smaller files, smoother playback, better colors.
- **One purpose.** BLIP isn’t a generic video. It's purpose is for short, looping, in-line animations.

---

## Getting Started

Want to add BLIP support to your platform, tool, or app?  
Start with [the spec](./spec.md), then check out our [reference implementation](https://github.com/blip-format/blip-reference).

If you find gaps or want to propose changes, open an issue or pull request!

---

## Contributing

We welcome feedback, clarifications, and proposals.  
- [Open an issue](https://github.com/blip-format/blip-spec/issues)
- [Submit a pull request](https://github.com/blip-format/blip-spec/pulls)
- [Discuss on GitHub Discussions](https://github.com/blip-format/blip-spec/discussions)

---

## License

This specification is open-source under the [MIT License](./LICENSE).

---
