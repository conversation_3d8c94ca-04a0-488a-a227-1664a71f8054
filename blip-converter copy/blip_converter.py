#!/usr/bin/env python3
import argparse
import os
import subprocess
import tempfile
from blip import Blip
from moviepy import VideoFile<PERSON>lip

def get_video_properties(input_path):
    """Gets video properties using ffprobe."""
    clip = VideoFileClip(input_path)
    width, height = clip.size
    frame_count = int(clip.duration * clip.fps)
    framerate = int(clip.fps)
    clip.close()
    return width, height, frame_count, framerate

def mp4_to_blip(input_path, output_path):
    """Converts an MP4 file to a .blip file."""
    print(f"Converting {input_path} to {output_path}...")
    width, height, frame_count, framerate = get_video_properties(input_path)

    with tempfile.NamedTemporaryFile(suffix=".av1", delete=False) as temp_payload:
        payload_path = temp_payload.name
        command = [
            "ffmpeg",
            "-i", input_path,
            "-c:v", "libaom-av1",
            "-strict", "-2",
            "-an",
            "-f", "ivf",
            payload_path,
        ]
        subprocess.run(command, check=True, capture_output=True)

        with open(payload_path, "rb") as f:
            video_payload = f.read()

    blip_image = Blip(
        width=width,
        height=height,
        frame_count=frame_count,
        framerate=framerate,
        video_payload=video_payload,
    )
    blip_image.encode(output_path)
    os.remove(payload_path)
    print("Conversion successful.")

def gif_to_blip(input_path, output_path):
    """Converts a GIF file to a .blip file."""
    print(f"Converting {input_path} to {output_path}...")
    width, height, frame_count, framerate = get_video_properties(input_path)

    with tempfile.NamedTemporaryFile(suffix=".av1", delete=False) as temp_payload:
        payload_path = temp_payload.name
        command = [
            "ffmpeg",
            "-i", input_path,
            "-c:v", "libaom-av1",
            "-strict", "-2",
            "-an",
            "-f", "ivf",
            payload_path,
        ]
        subprocess.run(command, check=True, capture_output=True)

        with open(payload_path, "rb") as f:
            video_payload = f.read()

    blip_image = Blip(
        width=width,
        height=height,
        frame_count=frame_count,
        framerate=framerate,
        video_payload=video_payload,
    )
    blip_image.encode(output_path)
    os.remove(payload_path)
    print("Conversion successful.")

def blip_to_mp4(input_path, output_path):
    """Converts a .blip file to an MP4 file."""
    print(f"Converting {input_path} to {output_path}...")
    blip_image = Blip.decode(input_path)

    with tempfile.NamedTemporaryFile(suffix=".av1", delete=False) as temp_payload:
        payload_path = temp_payload.name
        temp_payload.write(blip_image.video_payload)

    command = [
        "ffmpeg",
        "-r", str(blip_image.framerate),
        "-i", payload_path,
        "-c:v", "libx264",
        "-pix_fmt", "yuv420p",
        output_path,
    ]
    subprocess.run(command, check=True, capture_output=True)
    os.remove(payload_path)
    print("Conversion successful.")

def blip_to_gif(input_path, output_path):
    """Converts a .blip file to a GIF file."""
    print(f"Converting {input_path} to {output_path}...")
    blip_image = Blip.decode(input_path)

    with tempfile.NamedTemporaryFile(suffix=".av1", delete=False) as temp_payload:
        payload_path = temp_payload.name
        temp_payload.write(blip_image.video_payload)

    command = [
        "ffmpeg",
        "-r", str(blip_image.framerate),
        "-i", payload_path,
        output_path,
    ]
    subprocess.run(command, check=True, capture_output=True)
    os.remove(payload_path)
    print("Conversion successful.")

def main():
    parser = argparse.ArgumentParser(description="Convert files to and from the .blip format.")
    subparsers = parser.add_subparsers(dest="command", required=True)

    # mp4 to blip
    parser_mp4_to_blip = subparsers.add_parser("mp4toblip", help="Convert MP4 to BLIP")
    parser_mp4_to_blip.add_argument("input", help="Input MP4 file")
    parser_mp4_to_blip.add_argument("output", help="Output BLIP file")

    # gif to blip
    parser_gif_to_blip = subparsers.add_parser("giftoblip", help="Convert GIF to BLIP")
    parser_gif_to_blip.add_argument("input", help="Input GIF file")
    parser_gif_to_blip.add_argument("output", help="Output BLIP file")

    # blip to mp4
    parser_blip_to_mp4 = subparsers.add_parser("bliptomp4", help="Convert BLIP to MP4")
    parser_blip_to_mp4.add_argument("input", help="Input BLIP file")
    parser_blip_to_mp4.add_argument("output", help="Output MP4 file")

    # blip to gif
    parser_blip_to_gif = subparsers.add_parser("bliptogif", help="Convert BLIP to GIF")
    parser_blip_to_gif.add_argument("input", help="Input BLIP file")
    parser_blip_to_gif.add_argument("output", help="Output GIF file")

    args = parser.parse_args()

    if args.command == "mp4toblip":
        mp4_to_blip(args.input, args.output)
    elif args.command == "giftoblip":
        gif_to_blip(args.input, args.output)
    elif args.command == "bliptomp4":
        blip_to_mp4(args.input, args.output)
    elif args.command == "bliptogif":
        blip_to_gif(args.input, args.output)

if __name__ == "__main__":
    main()