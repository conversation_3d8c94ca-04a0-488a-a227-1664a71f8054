# BLIP Reference Implementation

This directory contains a Python-based reference implementation for a `.blip` file encoder and decoder, designed to align with the format specification.

## Files

- `blip_reference.py`: A Python script that defines a `Blip` class for encoding and decoding `.blip` files. It includes functions for handling the file header, video payload, and CRC32 checksum.

## Features

- **Encode and Decode:** The script can both create `.blip` files from raw data and parse existing `.blip` files into a structured format.
- **Specification Compliant:** It follows the `.blip` format specification, including the correct handling of the header, flags, and CRC32 checksum.
- **Example Usage:** The `main` function demonstrates how to create a `.blip` file, encode it, and then decode it to verify its integrity.

## How to Run

To run the reference implementation, execute the script from your terminal:

```bash
./blip_reference.py
```

The script will:
1. Create a sample `.blip` file with dummy data.
2. Encode the file and save it as `example.blip`.
3. Decode the file and print its header information.
4. Clean up by deleting the `example.blip` file.