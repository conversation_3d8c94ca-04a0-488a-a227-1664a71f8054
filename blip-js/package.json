{"name": "@blip-format/blip-js", "version": "1.0.0", "description": "A JavaScript library for playing .blip files in the browser.", "main": "dist/blip.umd.js", "module": "dist/blip.esm.js", "types": "dist/index.d.ts", "type": "module", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "rollup -c", "serve": "http-server ."}, "keywords": ["blip", "av1", "animation"], "author": "", "license": "MIT", "devDependencies": {"@rollup/plugin-node-resolve": "^15.0.1", "@rollup/plugin-typescript": "^11.0.0", "http-server": "^14.1.1", "rollup": "^3.15.0", "rollup-plugin-polyfill-node": "^0.13.0", "typescript": "^4.9.5"}, "dependencies": {"crc": "^4.3.2", "dav1d.js": "^0.1.1", "mp4box": "^1.4.5", "tslib": "^2.8.1"}}