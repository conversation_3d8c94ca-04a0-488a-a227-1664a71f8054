import typescript from '@rollup/plugin-typescript';
import { nodeResolve } from '@rollup/plugin-node-resolve';
import polyfill from 'rollup-plugin-polyfill-node';

export default {
  input: 'src/index.ts',
  output: [
    {
      file: 'dist/blip.esm.js',
      format: 'esm',
      sourcemap: true,
    },
    {
      file: 'dist/blip.umd.js',
      format: 'umd',
      name: 'Blip',
      sourcemap: true,
    },
  ],
  plugins: [
    typescript(),
    nodeResolve(),
    polyfill(),
  ],
};