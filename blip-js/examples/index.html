<!DOCTYPE html>
<html>
<head>
  <title>blip-js example</title>
</head>
<body>
  <h1>blip-js example</h1>

  <h2>Using the class-based API</h2>
  <canvas id="my-canvas"></canvas>
  <button id="play-button">Play</button>

  <h2>Using the custom HTML element</h2>
  <blip-player src="/blip-js/test.blip"></blip-player>

  <script src="/blip-js/dist/blip.umd.js"></script>
  <script>
    const canvas = document.getElementById('my-canvas');
    const player = new Blip.BlipPlayer('/blip-js/test.blip', canvas);
    player.load();

    const playButton = document.getElementById('play-button');
    playButton.addEventListener('click', () => {
      player.play();
    });
  </script>
</body>
</html>