# blip-js

A JavaScript library for playing .blip files in the browser.

## Installation

```bash
npm install @blip-format/blip-js
```

## Usage

### Class-based API

```javascript
import { BlipPlayer } from '@blip-format/blip-js';

const canvas = document.getElementById('my-canvas');
const player = new BlipPlayer('funnycat.blip', canvas);
player.load().then(() => {
  player.play();
});
```

### Custom HTML Element

```html
<script src="dist/blip.umd.js"></script>
<blip-player src="funnycat.blip"></blip-player>
```

## API

### `BlipPlayer`

#### `new BlipPlayer(src, canvas)`

-   `src`: The URL of the .blip file.
-   `canvas`: The canvas element to render the video on.

#### `load()`

Loads the .blip file and prepares it for playback. Returns a promise that resolves when the file is loaded.

#### `play()`

Starts playback of the .blip file.

#### `pause()`

Pauses playback of the .blip file.

#### `destroy()`

Stops playback and cleans up resources.

### `<blip-player>`

A custom HTML element that encapsulates the `BlipPlayer` logic.

#### Attributes

-   `src`: The URL of the .blip file.

## Running the Examples

Because of browser security restrictions, you need to serve the files from a web server to run the examples.

1.  Start a web server in the `blip-js` directory. A simple one is included with Python:

    ```bash
    python3 -m http.server 8000
    ```

2.  Open your browser to `http://localhost:8000/examples/`.

## Building

```bash
npm install
npm run build