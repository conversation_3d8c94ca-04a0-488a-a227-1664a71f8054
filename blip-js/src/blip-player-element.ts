import { BlipPlayer } from './blip-player';

export class BlipPlayerElement extends HTMLElement {
  private player: BlipPlayer;
  private canvas: HTMLCanvasElement;
  private playButton: HTMLButtonElement;

  constructor() {
    super();
    const shadow = this.attachShadow({ mode: 'open' });
    this.canvas = document.createElement('canvas');
    this.playButton = document.createElement('button');
    this.playButton.textContent = 'Play';
    shadow.appendChild(this.canvas);
    shadow.appendChild(this.playButton);
    this.player = new BlipPlayer(this.getAttribute('src') || '', this.canvas);
  }

  connectedCallback() {
    this.player.load();
    this.playButton.addEventListener('click', () => {
      this.player.play();
    });
  }

  disconnectedCallback() {
    this.player.destroy();
  }
}

customElements.define('blip-player', BlipPlayerElement);