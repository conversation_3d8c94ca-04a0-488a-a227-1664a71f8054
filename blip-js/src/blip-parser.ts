import { crc32 } from 'crc';

export interface BlipHeader {
  width: number;
  height: number;
  frameCount: number;
  framerate: number;
  transparency: boolean;
}

export class BlipParser {
  private dataView: DataView;

  constructor(arrayBuffer: ArrayBuffer) {
    this.dataView = new DataView(arrayBuffer);
  }

  public static async fromUrl(url: string): Promise<BlipParser> {
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`Failed to fetch: ${response.statusText}`);
    }
    const arrayBuffer = await response.arrayBuffer();
    return new BlipParser(arrayBuffer);
  }

  public parseHeader(): BlipHeader {
    const magic = this.dataView.getUint32(0, false);
    if (magic !== 0x424C4950) { // "BLIP"
      throw new Error('Invalid magic number');
    }

    const version = this.dataView.getUint8(4);
    if (version !== 1) {
      throw new Error(`Unsupported version: ${version}`);
    }

    const width = this.dataView.getUint16(5, true);
    const height = this.dataView.getUint16(7, true);
    const frameCount = this.dataView.getUint16(9, true);
    const framerate = this.dataView.getUint8(11);
    const flags = this.dataView.getUint8(12);
    const transparency = (flags & 0x01) === 0x01;

    return { width, height, frameCount, framerate, transparency };
  }

  public getVideoPayload(): Uint8Array {
    const headerSize = 13;
    const crc32Size = 4;
    const ivfFileHeaderSize = 12; // 12 bytes for IVF file header
    const ivfFrameHeaderSize = 12; // 12 bytes for IVF frame header (assuming one frame)

    // The video payload in a .blip file includes the IVF headers.
    // mp4box expects raw AV1 NAL units, so we need to skip these headers.
    const payloadStart = headerSize + ivfFileHeaderSize + ivfFrameHeaderSize;
    const payloadLength = this.dataView.byteLength - headerSize - crc32Size - ivfFileHeaderSize - ivfFrameHeaderSize;

    return new Uint8Array(this.dataView.buffer, payloadStart, payloadLength);
  }

  public validateCrc32(): boolean {
    const crc32Size = 4;
    const data = new Uint8Array(this.dataView.buffer, 0, this.dataView.byteLength - crc32Size);
    const expectedCrc = this.dataView.getUint32(this.dataView.byteLength - crc32Size, true);
    const actualCrc = crc32(data);
    return expectedCrc === actualCrc;
  }
}