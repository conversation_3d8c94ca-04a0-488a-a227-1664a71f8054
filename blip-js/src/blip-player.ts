import { Blip<PERSON>arser, BlipHeader } from './blip-parser';
import * as MP4Box from 'mp4box';
import Dav1d from 'dav1d.js';
import { WebGLRenderer } from './webgl-renderer';

export class BlipPlayer {
  private canvas: HTMLCanvasElement;
  private parser!: BlipParser;
  private header!: BlipHeader;
  private videoPayload!: Uint8Array;
  private video!: HTMLVideoElement;
  private mediaSource!: MediaSource;
  private sourceBuffer!: SourceBuffer;
  private isPlaying = false;
  private decoder!: any;
  private renderer!: WebGLRenderer;

  constructor(private src: string, canvas: HTMLCanvasElement) {
    this.canvas = canvas;
  }

  public async load() {
    this.parser = await BlipParser.fromUrl(this.src);
    if (!this.parser.validateCrc32()) {
      throw new Error('CRC32 checksum mismatch');
    }
    this.header = this.parser.parseHeader();
    this.videoPayload = this.parser.getVideoPayload();
    this.canvas.width = this.header.width;
    this.canvas.height = this.header.height;
  }

  public play() {
    console.log('play called');
    if (this.isPlaying) return;
    this.isPlaying = true;

    if (BlipPlayer.isAv1Supported()) {
      console.log('playing native');
      this.playNative();
    } else {
      console.log('playing wasm');
      this.playWasm();
    }
  }

  public pause() {
    if (!this.isPlaying) return;
    this.isPlaying = false;
    if (this.video) {
      this.video.pause();
    }
  }

  public destroy() {
    this.pause();
    if (this.video) {
      URL.revokeObjectURL(this.video.src);
    }
    if (this.decoder) {
      this.decoder.close();
    }
  }

  private playNative() {
    console.log('playNative called');
    this.video = document.createElement('video');
    this.mediaSource = new MediaSource();
    this.video.src = URL.createObjectURL(this.mediaSource);
    console.log('video src:', this.video.src);
    this.video.loop = true;

    this.mediaSource.addEventListener('sourceopen', () => {
      console.log('sourceopen');
      const mime = 'video/mp4; codecs="av01.0.05M.08"';
      console.log('mime:', mime);
      this.sourceBuffer = this.mediaSource.addSourceBuffer(mime);

      const mp4boxfile: any = MP4Box.createFile();
      console.log('mp4boxfile:', mp4boxfile);
      mp4boxfile.onMoovReady = (info: any) => {
        console.log('moov ready:', info);
        console.log('moov buffer:', info.moov);
        this.sourceBuffer.appendBuffer(info.moov);
      };

      this.sourceBuffer.addEventListener('updateend', () => {
        console.log('source buffer update end');
        if (!this.sourceBuffer.updating) {
          mp4boxfile.flush();
        }
      });

      const trackOptions = {
        timescale: 1000,
        width: this.header.width,
        height: this.header.height,
        nb_samples: this.header.frameCount,
        codec: 'av01.0.05M.08',
        description: this.createAv1cBox()
      };

      const trackId = mp4boxfile.addTrack(trackOptions);
      const sampleOptions = {
        duration: 1000 / this.header.framerate,
        is_sync: true
      };
      const videoBuffer = this.videoPayload.buffer;
      mp4boxfile.addSample(trackId, videoBuffer, sampleOptions);
    });

    this.video.play();
    this.renderLoop();
  }

  private async playWasm() {
    this.renderer = new WebGLRenderer(this.canvas);
    this.decoder = new Dav1d();
    await this.decoder.init();
    const decodedFrames = await this.decoder.decode(this.videoPayload);
    for (const frame of decodedFrames) {
      if (!this.isPlaying) break;
      this.renderer.render(frame);
      await new Promise(resolve => setTimeout(resolve, 1000 / this.header.framerate));
    }
  }

  private renderLoop() {
    if (!this.isPlaying) return;
    const ctx = this.canvas.getContext('2d');
    if (ctx) {
      ctx.drawImage(this.video, 0, 0, this.header.width, this.header.height);
    }
    requestAnimationFrame(() => this.renderLoop());
  }

  public static isAv1Supported(): boolean {
    return 'MediaSource' in window && MediaSource.isTypeSupported('video/mp4; codecs="av01.0.05M.08"');
  }

  private createAv1cBox(): Uint8Array {
    // See ISO/IEC 14496-15:2019, Section 8.3.3.2
    const av1c = new Uint8Array([
      0x81, // marker (1), version (7)
      0x00, // seq_profile (3), seq_level_idx_0 (5)
      0x00, // seq_tier_0 (1), high_bitdepth (1), twelve_bit (1), monochrome (1), chroma_subsampling_x (1), chroma_subsampling_y (1), chroma_sample_position (2)
      0x00, // reserved (3), initial_presentation_delay_present (1), initial_presentation_delay_minus_one (4)
    ]);
    // seq_profile = 0 (Main), seq_level_idx_0 = 13 (5.1)
    av1c[1] = (0 << 5) | 13;
    return av1c;
  }
}