#!/usr/bin/env python3
# Copy of the reference in blip-reference
import struct
import zlib
import os

MAGIC = b"BLIP"
VERSION = 1
HEADER_FORMAT = "<4sBHHHBB"  # Little-endian: magic, version, width, height, frame_count, framerate, flags
HEADER_SIZE = 13
CRC32_SIZE = 4


class Blip:
    """
    Represents a .blip image file, with methods to encode and decode.
    """

    def __init__(
        self, width, height, frame_count, framerate, video_payload, transparency=False
    ):
        """
        Initializes a Blip object with its properties.
        """
        self.version = VERSION
        self.width = width
        self.height = height
        self.frame_count = frame_count
        self.framerate = framerate
        self.flags = 0x01 if transparency else 0x00
        self.video_payload = video_payload

    @classmethod
    def decode(cls, file_path):
        """
        Decodes a .blip file from the given path and returns a Blip object.
        """
        with open(file_path, "rb") as f:
            data = f.read()

        if len(data) < HEADER_SIZE + CRC32_SIZE:
            raise ValueError("File is too small to be a valid .blip file.")

        header_data = data[:HEADER_SIZE]
        payload_data = data[HEADER_SIZE:-CRC32_SIZE]
        crc_from_file = data[-CRC32_SIZE:]

        expected_crc = zlib.crc32(header_data + payload_data)
        file_crc_val = struct.unpack("<I", crc_from_file)[0]
        if expected_crc != file_crc_val:
            raise ValueError(
                f"CRC32 checksum mismatch. Expected {expected_crc}, got {file_crc_val}."
            )

        magic, version, width, height, frame_count, framerate, flags = struct.unpack(
            HEADER_FORMAT, header_data
        )

        if magic != MAGIC:
            raise ValueError(f"Invalid magic number. Expected {MAGIC}, got {magic}.")

        if version != VERSION:
            print(
                f"Warning: Unsupported version. File version is {version}, decoder supports {VERSION}."
            )

        transparency = bool(flags & 0x01)

        if flags & 0xFE:
            print("Warning: Reserved flag bits are set. These should be ignored.")

        return cls(
            width=width,
            height=height,
            frame_count=frame_count,
            framerate=framerate,
            video_payload=payload_data,
            transparency=transparency,
        )

    def encode(self, file_path):
        """
        Encodes the Blip object into a .blip file at the given path.
        """

        header = struct.pack(
            HEADER_FORMAT,
            MAGIC,
            self.version,
            self.width,
            self.height,
            self.frame_count,
            self.framerate,
            self.flags,
        )

        data_to_checksum = header + self.video_payload
        crc = zlib.crc32(data_to_checksum)
        crc_bytes = struct.pack("<I", crc)

        with open(file_path, "wb") as f:
            f.write(data_to_checksum + crc_bytes)


def main():
    """
    Main function to demonstrate encoding and decoding a .blip file.
    """
    print("--- .blip Reference Implementation ---")

    width, height = 128, 96
    frame_count = 60
    framerate = 30
    dummy_payload = os.urandom(2048)

    blip_image = Blip(
        width=width,
        height=height,
        frame_count=frame_count,
        framerate=framerate,
        video_payload=dummy_payload,
        transparency=False,
    )

    file_path = "example.blip"

    try:
        print(f"\nEncoding Blip object to '{file_path}'...")
        blip_image.encode(file_path)
        print("Encoding successful.")
    except IOError as e:
        print(f"Error writing file: {e}")
        return

    try:
        print(f"\nDecoding '{file_path}'...")
        decoded_blip = Blip.decode(file_path)
        print("Decoding successful.")

        print("\n--- Decoded Header Data ---")
        print(f"  Version: {decoded_blip.version}")
        print(f"  Dimensions: {decoded_blip.width}x{decoded_blip.height}")
        print(f"  Frame Count: {decoded_blip.frame_count}")
        print(f"  Framerate: {decoded_blip.framerate} fps")
        print(f"  Transparency: {bool(decoded_blip.flags & 0x01)}")
        print(f"  Payload size: {len(decoded_blip.video_payload)} bytes")
        print("---------------------------\n")

    except (ValueError, IOError) as e:
        print(f"Error decoding file: {e}")
    finally:
        if os.path.exists(file_path):
            os.remove(file_path)
            print(f"Cleaned up '{file_path}'.")


if __name__ == "__main__":
    main()
