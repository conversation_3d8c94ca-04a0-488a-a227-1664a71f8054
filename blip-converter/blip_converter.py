#!/usr/bin/env python3
import argparse
import os
import subprocess
import tempfile
import json
from blip import Blip


def get_video_properties(input_path):
    """Gets video properties using ffprobe."""
    command = [
        "ffprobe",
        "-v",
        "error",
        "-select_streams",
        "v:0",
        "-show_entries",
        "stream=width,height,r_frame_rate,nb_frames",
        "-of",
        "json",
        input_path,
    ]
    result = subprocess.run(command, check=True, capture_output=True, text=True)
    data = json.loads(result.stdout)["streams"][0]
    width = int(data["width"])
    height = int(data["height"])

    num, den = data["r_frame_rate"].split("/")
    framerate = round(int(num) / int(den))

    command = [
        "ffprobe",
        "-v",
        "error",
        "-show_entries",
        "format=duration",
        "-of",
        "default=noprint_wrappers=1:nokey=1",
        input_path,
    ]
    result = subprocess.run(command, check=True, capture_output=True, text=True)
    duration = float(result.stdout)

    frame_count = int(duration * framerate)

    return width, height, frame_count, framerate


def print_size_difference(input_path, output_path):
    """Prints the file size difference between input and output files."""
    input_size = os.path.getsize(input_path)
    output_size = os.path.getsize(output_path)
    size_difference = output_size - input_size
    if input_size == 0:
        percentage_change = float("inf") if size_difference > 0 else 0
    else:
        percentage_change = (size_difference / input_size) * 100

    print(
        f"File size change: {input_size:,} bytes -> {output_size:,} bytes ({percentage_change:+.2f}%)"
    )


def mp4_to_blip(input_path, output_path):
    """Converts an MP4 file to a .blip file."""
    print(f"Converting {input_path} to {output_path}...")
    width, height, frame_count, framerate = get_video_properties(input_path)

    with tempfile.NamedTemporaryFile(suffix=".av1", delete=False) as temp_payload:
        payload_path = temp_payload.name
        command = [
            "ffmpeg",
            "-i",
            input_path,
            "-y",
            "-c:v",
            "libsvtav1",
            "-crf",
            "20",
            "-preset",
            "5",
            "-strict",
            "-2",
            "-an",
            "-f",
            "ivf",
            payload_path,
        ]
        subprocess.run(command, check=True, capture_output=True)

        with open(payload_path, "rb") as f:
            video_payload = f.read()

    blip_image = Blip(
        width=width,
        height=height,
        frame_count=frame_count,
        framerate=framerate,
        video_payload=video_payload,
    )
    blip_image.encode(output_path)
    os.remove(payload_path)
    print("Conversion successful.")
    print_size_difference(input_path, output_path)


def gif_to_blip(input_path, output_path):
    """Converts a GIF file to a .blip file."""
    print(f"Converting {input_path} to {output_path}...")
    width, height, frame_count, framerate = get_video_properties(input_path)

    with tempfile.NamedTemporaryFile(suffix=".av1", delete=False) as temp_payload:
        payload_path = temp_payload.name
        command = [
            "ffmpeg",
            "-i",
            input_path,
            "-y",
            "-c:v",
            "libsvtav1",
            "-crf",
            "20",
            "-preset",
            "5",
            "-strict",
            "-2",
            "-an",
            "-f",
            "ivf",
            payload_path,
        ]
        subprocess.run(command, check=True, capture_output=True)

        with open(payload_path, "rb") as f:
            video_payload = f.read()

    blip_image = Blip(
        width=width,
        height=height,
        frame_count=frame_count,
        framerate=framerate,
        video_payload=video_payload,
    )
    blip_image.encode(output_path)
    os.remove(payload_path)
    print("Conversion successful.")
    print_size_difference(input_path, output_path)


def blip_to_mp4(input_path, output_path):
    """Converts a .blip file to an MP4 file."""
    print(f"Converting {input_path} to {output_path}...")
    blip_image = Blip.decode(input_path)

    with tempfile.NamedTemporaryFile(suffix=".av1", delete=False) as temp_payload:
        payload_path = temp_payload.name
        temp_payload.write(blip_image.video_payload)

    command = [
        "ffmpeg",
        "-r",
        str(blip_image.framerate),
        "-i",
        payload_path,
        "-c:v",
        "libx264",
        "-preset",
        "slow",
        "-crf",
        "18",
        "-pix_fmt",
        "yuv420p",
        "-y",
        output_path,
    ]
    subprocess.run(command, check=True, capture_output=True)
    os.remove(payload_path)
    print("Conversion successful.")
    print_size_difference(input_path, output_path)


def blip_to_gif(input_path, output_path):
    """Converts a .blip file to a GIF file."""
    print(f"Converting {input_path} to {output_path}...")
    blip_image = Blip.decode(input_path)

    with tempfile.NamedTemporaryFile(suffix=".av1", delete=False) as temp_payload:
        payload_path = temp_payload.name
        temp_payload.write(blip_image.video_payload)

    command = [
        "ffmpeg",
        "-r",
        str(blip_image.framerate),
        "-i",
        payload_path,
        "-vf",
        f"fps={blip_image.framerate},scale={blip_image.width}:-1:flags=lanczos,split[s0][s1];[s0]palettegen[p];[s1][p]paletteuse",
        "-y",
        output_path,
    ]
    subprocess.run(command, check=True, capture_output=True)
    os.remove(payload_path)
    print("Conversion successful.")
    print_size_difference(input_path, output_path)


def main():
    parser = argparse.ArgumentParser(
        description="Convert files to and from the .blip format."
    )
    subparsers = parser.add_subparsers(dest="command", required=True)

    # mp4 to blip
    parser_mp4_to_blip = subparsers.add_parser("mp4toblip", help="Convert MP4 to BLIP")
    parser_mp4_to_blip.add_argument("input", help="Input MP4 file")
    parser_mp4_to_blip.add_argument("output", help="Output BLIP file")

    # gif to blip
    parser_gif_to_blip = subparsers.add_parser("giftoblip", help="Convert GIF to BLIP")
    parser_gif_to_blip.add_argument("input", help="Input GIF file")
    parser_gif_to_blip.add_argument("output", help="Output BLIP file")

    # blip to mp4
    parser_blip_to_mp4 = subparsers.add_parser("bliptomp4", help="Convert BLIP to MP4")
    parser_blip_to_mp4.add_argument("input", help="Input BLIP file")
    parser_blip_to_mp4.add_argument("output", help="Output MP4 file")

    # blip to gif
    parser_blip_to_gif = subparsers.add_parser("bliptogif", help="Convert BLIP to GIF")
    parser_blip_to_gif.add_argument("input", help="Input BLIP file")
    parser_blip_to_gif.add_argument("output", help="Output GIF file")

    args = parser.parse_args()

    if args.command == "mp4toblip":
        mp4_to_blip(args.input, args.output)
    elif args.command == "giftoblip":
        gif_to_blip(args.input, args.output)
    elif args.command == "bliptomp4":
        blip_to_mp4(args.input, args.output)
    elif args.command == "bliptogif":
        blip_to_gif(args.input, args.output)


if __name__ == "__main__":
    main()
